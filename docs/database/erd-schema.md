# Entity Relationship Diagram (ERD) - Full Database Schema

## Tổng quan

Tài liệu này mô tả complete Entity Relationship Diagram cho Blog API v3 database schema, bao gồm tất cả tables, relationships, và constraints.

## Database Overview

### Database Statistics
- **Total Tables**: 45+ tables
- **Engine**: InnoDB
- **Charset**: utf8mb4_unicode_ci
- **Primary Keys**: INT UNSIGNED AUTO_INCREMENT
- **Foreign Keys**: Cascading deletes where appropriate
- **Indexes**: Optimized for multi-tenant queries

## Complete ERD Diagram

```mermaid
erDiagram
    %% Tenant & Website Management
    tenant ||--o{ website : "has"
    tenant ||--o{ tenant_user : "has_members"
    global_user ||--o{ tenant_user : "belongs_to"
    website ||--o{ website_user_role : "has_user_roles"
    tenant_user ||--o{ website_user_role : "has_roles"
    
    %% User Management
    global_user ||--o{ user_session : "has_sessions"
    website ||--o{ user_session : "current_context"
    tenant ||--o{ user_session : "current_context"
    
    %% RBAC System
    website ||--o{ role : "has_roles"
    role ||--o{ website_user_role : "assigned_to"
    role ||--o{ role_permission : "has_permissions"
    permission ||--o{ role_permission : "granted_to"
    
    %% Blog Content
    website ||--o{ category : "has"
    website ||--o{ tag : "has"
    website ||--o{ post : "has"
    website ||--o{ author : "has"
    category ||--o{ category : "parent_child"
    category ||--o{ post : "categorizes"
    tag ||--o{ post_tag : "tags"
    post ||--o{ post_tag : "tagged_with"
    author ||--o{ post : "writes"
    post ||--o{ post_translation : "translated_as"
    post ||--o{ comment : "has_comments"
    post ||--o{ post_meta : "has_metadata"
    post ||--o{ post_revision : "has_revisions"
    
    %% Media Management
    website ||--o{ media : "stores"
    tenant_user ||--o{ media : "uploads"
    media ||--o{ media_folder : "organized_in"
    
    %% Notifications
    website ||--o{ notification_template : "has_templates"
    website ||--o{ notification : "sends"
    tenant_user ||--o{ notification : "receives"
    notification_template ||--o{ notification : "uses_template"
    
    %% Onboarding
    website ||--o{ onboarding_journey : "has_journeys"
    onboarding_journey ||--o{ onboarding_step : "has_steps"
    tenant_user ||--o{ user_progress : "tracks_progress"
    onboarding_journey ||--o{ user_progress : "progresses_through"
    
    %% Website Management
    website ||--o{ page : "has_pages"
    website ||--o{ menu : "has_menus"
    website ||--o{ widget : "has_widgets"
    website ||--o{ theme_setting : "has_theme_settings"
    
    %% SEO
    website ||--o{ seo_meta : "has_seo"
    website ||--o{ url_redirect : "has_redirects"
    website ||--o{ sitemap : "has_sitemaps"
    post ||--o{ seo_meta : "optimized_with"
    page ||--o{ seo_meta : "optimized_with"
    
    %% Payment System
    website ||--o{ subscription_plan : "offers_plans"
    website ||--o{ payment : "processes_payments"
    tenant_user ||--o{ customer : "is_customer"
    customer ||--o{ payment : "makes_payments"
    customer ||--o{ subscription : "has_subscriptions"
    subscription_plan ||--o{ subscription : "subscribed_to"
    payment ||--o{ invoice : "generates_invoice"
    
    %% Socket/Real-time
    website ||--o{ socket_connection : "has_connections"
    tenant_user ||--o{ socket_connection : "connects"
    socket_connection ||--o{ socket_room : "joins_rooms"
    
    %% Analytics & Logs
    website ||--o{ analytics_event : "tracks_events"
    tenant_user ||--o{ analytics_event : "generates_events"
    website ||--o{ activity_log : "logs_activities"
    tenant_user ||--o{ activity_log : "performs_activities"
    
    %% System Tables
    website ||--o{ plugin_config : "configures_plugins"
    website ||--o{ setting : "has_settings"
    seeder_log ||--o{ tenant : "seeds"
    seeder_log ||--o{ website : "seeds"
```

## Core Entity Definitions

### 1. Tenant & User Management

#### Global User Entity
```sql
global_user {
    INT UNSIGNED id PK
    VARCHAR(320) email UK "Global unique email"
    TIMESTAMP email_verified_at
    VARCHAR(255) password_hash
    VARCHAR(50) first_name
    VARCHAR(50) last_name
    VARCHAR(500) avatar_url
    VARCHAR(50) timezone
    VARCHAR(10) locale
    ENUM status "active,inactive,suspended,banned"
    TIMESTAMP last_login_at
    TIMESTAMP created_at
    TIMESTAMP updated_at
    TIMESTAMP deleted_at
}
```

#### Tenant Entity
```sql
tenant {
    INT UNSIGNED id PK
    VARCHAR(100) name
    VARCHAR(100) slug UK
    VARCHAR(320) email UK
    VARCHAR(20) phone
    VARCHAR(500) logo_url
    ENUM status "active,inactive,suspended"
    ENUM plan_type "free,basic,pro,enterprise"
    JSON settings
    TIMESTAMP created_at
    TIMESTAMP updated_at
    TIMESTAMP deleted_at
}
```

#### Website Entity
```sql
website {
    INT UNSIGNED id PK
    INT UNSIGNED tenant_id FK
    VARCHAR(100) name
    VARCHAR(100) slug
    VARCHAR(255) domain UK
    JSON alternate_domains
    VARCHAR(255) title
    TEXT description
    VARCHAR(500) logo_url
    VARCHAR(500) favicon_url
    JSON theme_config
    JSON seo_config
    ENUM status "active,inactive,maintenance"
    TINYINT is_default
    TIMESTAMP created_at
    TIMESTAMP updated_at
    TIMESTAMP deleted_at
}
```

#### Tenant User Membership
```sql
tenant_user {
    INT UNSIGNED id PK
    INT UNSIGNED global_user_id FK
    INT UNSIGNED tenant_id FK
    VARCHAR(50) local_username
    VARCHAR(100) display_name
    TEXT bio
    ENUM status "active,inactive,pending,suspended"
    TIMESTAMP joined_at
    INT UNSIGNED invited_by FK
    VARCHAR(255) invitation_token
    TIMESTAMP invitation_expires_at
    TIMESTAMP last_activity_at
    JSON preferences
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

### 2. RBAC System

#### Role Entity
```sql
role {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    VARCHAR(100) name
    VARCHAR(100) slug
    TEXT description
    TINYINT is_system
    TINYINT is_default
    TINYINT level
    JSON permissions
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

#### Permission Entity
```sql
permission {
    INT UNSIGNED id PK
    VARCHAR(100) name
    VARCHAR(100) slug UK
    TEXT description
    VARCHAR(50) category
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

#### Website User Role
```sql
website_user_role {
    INT UNSIGNED id PK
    INT UNSIGNED tenant_user_id FK
    INT UNSIGNED website_id FK
    INT UNSIGNED role_id FK
    INT UNSIGNED assigned_by FK
    TIMESTAMP assigned_at
    TIMESTAMP expires_at
    TINYINT is_active
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

### 3. Blog Content System

#### Category Entity (Nested Set Model)
```sql
category {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    VARCHAR(100) name
    VARCHAR(100) slug
    TEXT description
    INT UNSIGNED parent_id FK
    INT UNSIGNED lft "Left boundary"
    INT UNSIGNED rgt "Right boundary"
    TINYINT level
    SMALLINT sort_order
    VARCHAR(500) image_url
    VARCHAR(255) meta_title
    VARCHAR(500) meta_description
    TINYINT is_active
    INT UNSIGNED post_count
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

#### Tag Entity
```sql
tag {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    VARCHAR(100) name
    VARCHAR(100) slug
    TEXT description
    VARCHAR(7) color "Hex color"
    INT UNSIGNED post_count
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

#### Post Entity
```sql
post {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    VARCHAR(255) title
    VARCHAR(255) slug
    TEXT excerpt
    LONGTEXT content
    ENUM content_type "html,markdown,json"
    INT UNSIGNED author_id FK
    INT UNSIGNED category_id FK
    VARCHAR(500) featured_image_url
    ENUM status "draft,review,published,archived"
    ENUM visibility "public,private,protected,password"
    VARCHAR(255) password
    TINYINT is_featured
    TINYINT is_sticky
    TINYINT allow_comments
    INT UNSIGNED view_count
    INT UNSIGNED like_count
    INT UNSIGNED comment_count
    INT UNSIGNED share_count
    INT UNSIGNED reading_time "minutes"
    TIMESTAMP published_at
    TIMESTAMP scheduled_at
    VARCHAR(255) meta_title
    VARCHAR(500) meta_description
    VARCHAR(500) meta_keywords
    TINYINT seo_score "0-100"
    TIMESTAMP created_at
    TIMESTAMP updated_at
    TIMESTAMP deleted_at
}
```

#### Author Entity
```sql
author {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    INT UNSIGNED user_id FK
    VARCHAR(100) display_name
    VARCHAR(100) slug
    TEXT bio
    VARCHAR(500) avatar_url
    VARCHAR(500) website_url
    JSON social_links
    TINYINT is_active
    INT UNSIGNED post_count
    INT UNSIGNED follower_count
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

### 4. Media Management

#### Media Entity
```sql
media {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    INT UNSIGNED user_id FK
    VARCHAR(255) filename
    VARCHAR(255) stored_filename
    VARCHAR(255) title
    VARCHAR(255) alt_text
    TEXT description
    VARCHAR(100) mime_type
    INT UNSIGNED file_size "bytes"
    INT UNSIGNED width "pixels"
    INT UNSIGNED height "pixels"
    INT UNSIGNED duration "seconds"
    VARCHAR(20) storage_driver "local,s3,minio"
    VARCHAR(500) storage_path
    VARCHAR(500) public_url
    VARCHAR(500) cdn_url
    JSON variants "thumbnails,sizes"
    JSON metadata
    VARCHAR(500) folder_path
    TINYINT is_optimized
    INT UNSIGNED download_count
    TIMESTAMP created_at
    TIMESTAMP updated_at
    TIMESTAMP deleted_at
}
```

### 5. Notification System

#### Notification Template Entity
```sql
notification_template {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    VARCHAR(100) name
    VARCHAR(100) slug
    VARCHAR(255) subject
    LONGTEXT body_html
    LONGTEXT body_text
    JSON variables
    TINYINT is_active
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

#### Notification Entity
```sql
notification {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    INT UNSIGNED user_id FK
    VARCHAR(50) type
    VARCHAR(255) title
    TEXT message
    JSON data
    JSON channels "email,sms,push,slack"
    ENUM status "pending,sent,failed,delivered,read"
    ENUM priority "low,normal,high,urgent"
    TIMESTAMP scheduled_at
    TIMESTAMP sent_at
    TIMESTAMP read_at
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

### 6. Payment System

#### Subscription Plan Entity
```sql
subscription_plan {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    VARCHAR(100) name
    VARCHAR(100) slug
    TEXT description
    DECIMAL(10,2) price
    VARCHAR(3) currency
    ENUM billing_period "monthly,yearly,lifetime"
    JSON features
    INT max_users
    INT max_storage "bytes"
    INT max_bandwidth "bytes"
    TINYINT is_active
    TINYINT is_featured
    INT sort_order
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

#### Payment Entity
```sql
payment {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    INT UNSIGNED customer_id FK
    VARCHAR(100) payment_id "External payment ID"
    DECIMAL(10,2) amount
    VARCHAR(3) currency
    ENUM status "pending,processing,completed,failed,cancelled,refunded"
    ENUM payment_method "card,paypal,bank_transfer,crypto"
    VARCHAR(100) provider "stripe,paypal,square"
    TEXT description
    JSON provider_data
    JSON metadata
    TIMESTAMP processed_at
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

### 7. SEO Management

#### SEO Meta Entity
```sql
seo_meta {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    VARCHAR(50) entity_type "post,page,category"
    INT UNSIGNED entity_id
    VARCHAR(255) title
    VARCHAR(500) description
    VARCHAR(500) keywords
    VARCHAR(255) canonical_url
    JSON og_tags "Open Graph"
    JSON twitter_tags
    JSON structured_data
    VARCHAR(255) robots
    TINYINT seo_score "0-100"
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

#### URL Redirect Entity
```sql
url_redirect {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    VARCHAR(500) from_url
    VARCHAR(500) to_url
    ENUM redirect_type "301,302,307,308"
    TINYINT is_active
    INT hit_count
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

### 8. Real-time Communication

#### Socket Connection Entity
```sql
socket_connection {
    INT UNSIGNED id PK
    INT UNSIGNED website_id FK
    INT UNSIGNED user_id FK
    VARCHAR(255) connection_id
    VARCHAR(255) socket_id
    JSON metadata
    TINYINT is_active
    TIMESTAMP connected_at
    TIMESTAMP last_ping_at
    TIMESTAMP disconnected_at
    TIMESTAMP created_at
    TIMESTAMP updated_at
}
```

## Database Relationships Summary

### Primary Relationships

1. **Tenant → Websites** (1:N)
   - One tenant can have multiple websites
   - Website belongs to exactly one tenant

2. **Global User → Tenant Memberships** (1:N)
   - One user can belong to multiple tenants
   - Each membership is unique per user-tenant pair

3. **Tenant User → Website Roles** (1:N)
   - User can have different roles on different websites
   - Role assignments are website-specific

4. **Website → Content** (1:N)
   - Posts, categories, tags belong to specific website
   - Content is isolated per website

5. **Post → Tags** (N:M)
   - Posts can have multiple tags
   - Tags can be used by multiple posts

6. **Category → Posts** (1:N with hierarchy)
   - Categories use nested set model
   - Posts belong to one primary category

### Key Constraints

1. **Tenant Isolation**
   - All content tables include website_id
   - Foreign keys ensure data isolation

2. **User Management**
   - Global users with tenant-specific memberships
   - Role assignments are website-scoped

3. **Content Hierarchy**
   - Nested categories with lft/rgt boundaries
   - Post status workflow enforcement

4. **Referential Integrity**
   - Cascade deletes for dependent data
   - Restrict deletes for critical references

## Index Strategy

### Primary Indexes
```sql
-- Every table has auto-increment primary key
id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY
```

### Unique Indexes
```sql
-- Global uniqueness
UNIQUE KEY uk_global_user_email (email)
UNIQUE KEY uk_tenant_slug (slug)

-- Tenant-scoped uniqueness  
UNIQUE KEY uk_website_domain (domain)
UNIQUE KEY uk_post_slug_website (slug, website_id)
UNIQUE KEY uk_category_slug_website (slug, website_id)
```

### Performance Indexes
```sql
-- Multi-tenant query optimization
INDEX idx_post_website_status (website_id, status)
INDEX idx_post_website_published (website_id, published_at)
INDEX idx_media_website_folder (website_id, folder_path)

-- Foreign key indexes
INDEX idx_post_author_id (author_id)
INDEX idx_post_category_id (category_id)
INDEX idx_notification_user_id (user_id)

-- Search optimization
FULLTEXT idx_post_search (title, content)
INDEX idx_post_published_at (published_at)
```

### Composite Indexes
```sql
-- Complex query optimization
INDEX idx_post_list (website_id, status, published_at, id)
INDEX idx_user_session_active (global_user_id, is_active, expires_at)
INDEX idx_notification_pending (website_id, status, scheduled_at)
```

## Data Size Estimates

### Storage Requirements (per website)

| Entity | Estimated Records | Storage per Record | Total Storage |
|--------|------------------|-------------------|---------------|
| Users | 1,000 - 100,000 | 1KB | 1MB - 100MB |
| Posts | 100 - 50,000 | 10KB | 1MB - 500MB |
| Media | 500 - 250,000 | 2KB (metadata) | 1MB - 500MB |
| Comments | 1,000 - 500,000 | 500B | 500KB - 250MB |
| Notifications | 10,000 - 1M | 1KB | 10MB - 1GB |
| Analytics | 100,000 - 10M | 200B | 20MB - 2GB |

### Total Database Size (estimated)
- **Small Website**: 50MB - 100MB
- **Medium Website**: 500MB - 2GB  
- **Large Website**: 5GB - 50GB
- **Enterprise**: 50GB+

## Performance Considerations

### Query Optimization
1. **Always filter by website_id first**
2. **Use covering indexes for common queries**
3. **Implement query result caching**
4. **Use read replicas for reporting**

### Scaling Strategies
1. **Horizontal sharding by website_id**
2. **Separate read/write databases**
3. **Archive old data to cold storage**
4. **Implement connection pooling**

### Maintenance
1. **Regular index optimization**
2. **Partition large tables by date**
3. **Automated backup strategies**
4. **Monitor slow query logs**